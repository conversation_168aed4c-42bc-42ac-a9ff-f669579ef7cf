import nigui

# Initialize the GUI
app.init()

# Create the main window
var window = newWindow("ZView")

# Set window properties
window.width = 1200
window.height = 1000
window.resizable = false

# Create a main container that fills the window
var container = newLayoutContainer(Layout_Vertical)

# Create a custom drawing area for the dark background and border
var drawingArea = newControl()
drawingArea.widthMode = WidthMode_Fill
drawingArea.heightMode = HeightMode_Fill

# Custom paint event for dark background and border
drawingArea.onDraw = proc(event: DrawEvent) =
  let canvas = event.control.canvas
  let width = event.control.width
  let height = event.control.height

  # Fill background with dark grey (#1b1b1b)
  canvas.areaColor = rgb(27, 27, 27)
  canvas.fill()

  # Draw border with grey color (#333333)
  canvas.lineColor = rgb(51, 51, 51)
  canvas.lineWidth = 1

  # Draw border rectangle
  canvas.drawRectOutline(0, 0, width, height)

  # Draw inner border for thicker appearance
  canvas.drawRectOutline(1, 1, width - 2, height - 2)

# Add the drawing area to the container
container.add(drawingArea)

# Add the container to the window
window.add(container)

# Center the window on screen
window.centerOnScreen()

# Show the window
window.show()

# Start the GUI event loop
app.run()
