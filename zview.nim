import nigui
import math

# Initialize the GUI
app.init()

# Create the main window
var window = newWindow("")

# Set window properties
window.width = 1200
window.height = 1000
window.resizable = false

# Create a main container that fills the window
var container = newLayoutContainer(Layout_Vertical)

# Create a custom drawing area for the dark background, border and rounded corners
var drawingArea = newControl()
drawingArea.widthMode = WidthMode_Fill
drawingArea.heightMode = HeightMode_Fill

# Custom paint event for dark background, border and rounded corners
drawingArea.onDraw = proc(event: DrawEvent) =
  let canvas = event.control.canvas
  let width = event.control.width
  let height = event.control.height
  let cornerRadius = 15

  # Fill entire area with dark grey background (#1b1b1b)
  canvas.areaColor = rgb(27, 27, 27)
  canvas.fill()

  # Draw rounded rectangle with border
  canvas.lineColor = rgb(51, 51, 51)  # #333333 border color
  canvas.lineWidth = 2

  # Draw the main rectangle (without corners)
  canvas.drawRectOutline(cornerRadius, 0, width - 2 * cornerRadius, height)
  canvas.drawRectOutline(0, cornerRadius, width, height - 2 * cornerRadius)

  # Draw rounded corners using arcs (approximated with lines)
  let steps = 20
  for i in 0..steps:
    let angle = float(i) * PI / (2.0 * float(steps))
    let cosAngle = cos(angle)
    let sinAngle = sin(angle)

    # Top-left corner
    let x1 = cornerRadius - int(float(cornerRadius) * cosAngle)
    let y1 = cornerRadius - int(float(cornerRadius) * sinAngle)
    if i > 0:
      let prevAngle = float(i-1) * PI / (2.0 * float(steps))
      let prevX = cornerRadius - int(float(cornerRadius) * cos(prevAngle))
      let prevY = cornerRadius - int(float(cornerRadius) * sin(prevAngle))
      canvas.drawLine(prevX, prevY, x1, y1)

    # Top-right corner
    let x2 = width - cornerRadius + int(float(cornerRadius) * cosAngle)
    let y2 = cornerRadius - int(float(cornerRadius) * sinAngle)
    if i > 0:
      let prevAngle = float(i-1) * PI / (2.0 * float(steps))
      let prevX = width - cornerRadius + int(float(cornerRadius) * cos(prevAngle))
      let prevY = cornerRadius - int(float(cornerRadius) * sin(prevAngle))
      canvas.drawLine(prevX, prevY, x2, y2)

    # Bottom-right corner
    let x3 = width - cornerRadius + int(float(cornerRadius) * cosAngle)
    let y3 = height - cornerRadius + int(float(cornerRadius) * sinAngle)
    if i > 0:
      let prevAngle = float(i-1) * PI / (2.0 * float(steps))
      let prevX = width - cornerRadius + int(float(cornerRadius) * cos(prevAngle))
      let prevY = height - cornerRadius + int(float(cornerRadius) * sin(prevAngle))
      canvas.drawLine(prevX, prevY, x3, y3)

    # Bottom-left corner
    let x4 = cornerRadius - int(float(cornerRadius) * cosAngle)
    let y4 = height - cornerRadius + int(float(cornerRadius) * sinAngle)
    if i > 0:
      let prevAngle = float(i-1) * PI / (2.0 * float(steps))
      let prevX = cornerRadius - int(float(cornerRadius) * cos(prevAngle))
      let prevY = height - cornerRadius + int(float(cornerRadius) * sin(prevAngle))
      canvas.drawLine(prevX, prevY, x4, y4)

# Add the drawing area to the container
container.add(drawingArea)

# Add the container to the window
window.add(container)

# Center the window on screen
window.centerOnScreen()

# Show the window
window.show()

# Start the GUI event loop
app.run()
