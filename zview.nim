import nigui

# Initialize the GUI
app.init()

# Create the main window
var window = newWindow()

# Set window properties
window.width = 1200
window.height = 1000
window.resizable = false

# Remove title bar and make borderless
window.frame = false

# Set window background color to dark grey (#1b1b1b)
window.backgroundColor = rgb(27, 27, 27)

# Create a custom control for rounded corners and border
var mainPanel = newControl()
mainPanel.backgroundColor = rgb(27, 27, 27)  # #1b1b1b background

# Set the panel to fill the entire window
mainPanel.widthMode = WidthMode_Fill
mainPanel.heightMode = HeightMode_Fill

# Custom paint event for rounded corners and border
mainPanel.onDraw = proc(event: DrawEvent) =
  let canvas = event.control.canvas
  let width = event.control.width
  let height = event.control.height

  # Fill background with dark grey
  canvas.areaColor = rgb(27, 27, 27)  # #1b1b1b
  canvas.fill()

  # Draw border with grey color
  canvas.lineColor = rgb(51, 51, 51)  # #333333
  canvas.lineWidth = 1

  # Draw rounded rectangle border (approximated with lines)
  let cornerRadius = 10

  # Top border
  canvas.drawLine(cornerRadius, 0, width - cornerRadius, 0)
  # Right border
  canvas.drawLine(width - 1, cornerRadius, width - 1, height - cornerRadius)
  # Bottom border
  canvas.drawLine(width - cornerRadius, height - 1, cornerRadius, height - 1)
  # Left border
  canvas.drawLine(0, height - cornerRadius, 0, cornerRadius)

  # Draw corner arcs (simplified as small lines)
  # Top-left corner
  for i in 0..<cornerRadius:
    let x = cornerRadius - i
    let y = i
    if x >= 0 and y >= 0:
      canvas.drawLine(x, y, x, y)

  # Top-right corner
  for i in 0..<cornerRadius:
    let x = width - cornerRadius + i
    let y = i
    if x < width and y >= 0:
      canvas.drawLine(x, y, x, y)

  # Bottom-right corner
  for i in 0..<cornerRadius:
    let x = width - cornerRadius + i
    let y = height - i - 1
    if x < width and y < height:
      canvas.drawLine(x, y, x, y)

  # Bottom-left corner
  for i in 0..<cornerRadius:
    let x = cornerRadius - i
    let y = height - i - 1
    if x >= 0 and y < height:
      canvas.drawLine(x, y, x, y)

# Add the main panel to the window
window.add(mainPanel)

# Center the window on screen
window.centerOnScreen()

# Show the window
window.show()

# Start the GUI event loop
app.run()
