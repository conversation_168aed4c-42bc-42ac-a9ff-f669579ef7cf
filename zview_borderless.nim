import nigui
import math
when defined(windows):
  import winlean

# Initialize the GUI
app.init()

# Create the main window with no title
var window = newWindow("")

# Set window properties
window.width = 1200
window.height = 1000
window.resizable = false

# Windows-specific code to remove title bar and create borderless window
when defined(windows):
  # Get the window handle after showing
  window.show()
  
  # Get window handle (this is a simplified approach)
  # In a real implementation, you'd need to access the internal window handle
  # For now, we'll work with what nigui provides
  
  # Hide the window temporarily to modify it
  window.hide()

# Create a main container that fills the window
var container = newLayoutContainer(Layout_Vertical)

# Create a custom drawing area for the dark background, border and rounded corners
var drawingArea = newControl()
drawingArea.widthMode = WidthMode_Fill
drawingArea.heightMode = HeightMode_Fill

# Custom paint event for dark background, border and rounded corners
drawingArea.onDraw = proc(event: DrawEvent) =
  let canvas = event.control.canvas
  let width = event.control.width
  let height = event.control.height
  let cornerRadius = 20
  
  # Fill entire area with dark grey background (#1b1b1b)
  canvas.areaColor = rgb(27, 27, 27)
  canvas.fill()
  
  # Create rounded rectangle effect
  canvas.lineColor = rgb(51, 51, 51)  # #333333 border color
  canvas.lineWidth = 2
  
  # Draw the main body (rectangle without corners)
  # Top edge
  canvas.drawLine(cornerRadius, 1, width - cornerRadius, 1)
  # Right edge
  canvas.drawLine(width - 2, cornerRadius, width - 2, height - cornerRadius)
  # Bottom edge
  canvas.drawLine(width - cornerRadius, height - 2, cornerRadius, height - 2)
  # Left edge
  canvas.drawLine(1, height - cornerRadius, 1, cornerRadius)
  
  # Draw rounded corners using multiple small lines to approximate curves
  let steps = 15
  for i in 0..steps:
    let angle = float(i) * PI / (2.0 * float(steps))
    let x_offset = int(float(cornerRadius) * cos(angle))
    let y_offset = int(float(cornerRadius) * sin(angle))
    
    # Top-left corner
    let topLeftX = cornerRadius - x_offset
    let topLeftY = cornerRadius - y_offset
    if i > 0:
      let prev_angle = float(i-1) * PI / (2.0 * float(steps))
      let prev_x = cornerRadius - int(float(cornerRadius) * cos(prev_angle))
      let prev_y = cornerRadius - int(float(cornerRadius) * sin(prev_angle))
      canvas.drawLine(prev_x, prev_y, topLeftX, topLeftY)
    
    # Top-right corner
    let topRightX = width - cornerRadius + x_offset
    let topRightY = cornerRadius - y_offset
    if i > 0:
      let prev_angle = float(i-1) * PI / (2.0 * float(steps))
      let prev_x = width - cornerRadius + int(float(cornerRadius) * cos(prev_angle))
      let prev_y = cornerRadius - int(float(cornerRadius) * sin(prev_angle))
      canvas.drawLine(prev_x, prev_y, topRightX, topRightY)

    # Bottom-right corner
    let bottomRightX = width - cornerRadius + x_offset
    let bottomRightY = height - cornerRadius + y_offset
    if i > 0:
      let prev_angle = float(i-1) * PI / (2.0 * float(steps))
      let prev_x = width - cornerRadius + int(float(cornerRadius) * cos(prev_angle))
      let prev_y = height - cornerRadius + int(float(cornerRadius) * sin(prev_angle))
      canvas.drawLine(prev_x, prev_y, bottomRightX, bottomRightY)

    # Bottom-left corner
    let bottomLeftX = cornerRadius - x_offset
    let bottomLeftY = height - cornerRadius + y_offset
    if i > 0:
      let prev_angle = float(i-1) * PI / (2.0 * float(steps))
      let prev_x = cornerRadius - int(float(cornerRadius) * cos(prev_angle))
      let prev_y = height - cornerRadius + int(float(cornerRadius) * sin(prev_angle))
      canvas.drawLine(prev_x, prev_y, bottomLeftX, bottomLeftY)
  
  # Fill the rounded corners to make them look smoother
  canvas.areaColor = rgb(27, 27, 27)  # Same as background
  
  # Draw additional border lines for better definition
  canvas.lineColor = rgb(51, 51, 51)
  canvas.lineWidth = 1
  
  # Inner border for depth
  let innerRadius = cornerRadius - 3
  if innerRadius > 0:
    # Inner top edge
    canvas.drawLine(innerRadius, 3, width - innerRadius, 3)
    # Inner right edge
    canvas.drawLine(width - 4, innerRadius, width - 4, height - innerRadius)
    # Inner bottom edge
    canvas.drawLine(width - innerRadius, height - 4, innerRadius, height - 4)
    # Inner left edge
    canvas.drawLine(3, height - innerRadius, 3, innerRadius)

# Add the drawing area to the container
container.add(drawingArea)

# Add the container to the window
window.add(container)

# Center the window on screen
window.centerOnScreen()

# Show the window
window.show()

# Start the GUI event loop
app.run()
