import nigui
import math

# Initialize the GUI
app.init()

# Create the main window with minimal title
var window = newWindow("")

# Set window properties
window.width = 1200
window.height = 1000
window.resizable = false

# Create a main container that fills the window
var container = newLayoutContainer(Layout_Vertical)

# Create a custom drawing area for the dark background, border and rounded corners
var drawingArea = newControl()
drawingArea.widthMode = WidthMode_Fill
drawingArea.heightMode = HeightMode_Fill

# Custom paint event for dark background, border and rounded corners
drawingArea.onDraw = proc(event: DrawEvent) =
  let canvas = event.control.canvas
  let width = event.control.width
  let height = event.control.height
  let cornerRadius = 25  # Larger radius for more pronounced rounded corners
  
  # Fill entire area with dark grey background (#1b1b1b)
  canvas.areaColor = rgb(27, 27, 27)
  canvas.fill()
  
  # Draw the main border with rounded corners
  canvas.lineColor = rgb(51, 51, 51)  # #333333 border color
  canvas.lineWidth = 2
  
  # Draw the straight edges (avoiding corners)
  # Top edge
  canvas.drawLine(cornerRadius, 2, width - cornerRadius, 2)
  # Right edge
  canvas.drawLine(width - 3, cornerRadius, width - 3, height - cornerRadius)
  # Bottom edge
  canvas.drawLine(width - cornerRadius, height - 3, cornerRadius, height - 3)
  # Left edge
  canvas.drawLine(2, height - cornerRadius, 2, cornerRadius)
  
  # Draw smooth rounded corners using multiple points
  let steps = 25  # More steps for smoother curves
  for i in 0..steps:
    let angle = float(i) * PI / (2.0 * float(steps))
    let cosVal = cos(angle)
    let sinVal = sin(angle)
    
    # Calculate current point
    let xOffset = int(float(cornerRadius) * cosVal)
    let yOffset = int(float(cornerRadius) * sinVal)
    
    if i > 0:
      # Calculate previous point for line drawing
      let prevAngle = float(i-1) * PI / (2.0 * float(steps))
      let prevXOffset = int(float(cornerRadius) * cos(prevAngle))
      let prevYOffset = int(float(cornerRadius) * sin(prevAngle))
      
      # Top-left corner
      let currTLX = cornerRadius - xOffset
      let currTLY = cornerRadius - yOffset
      let prevTLX = cornerRadius - prevXOffset
      let prevTLY = cornerRadius - prevYOffset
      canvas.drawLine(prevTLX, prevTLY, currTLX, currTLY)
      
      # Top-right corner
      let currTRX = width - cornerRadius + xOffset
      let currTRY = cornerRadius - yOffset
      let prevTRX = width - cornerRadius + prevXOffset
      let prevTRY = cornerRadius - prevYOffset
      canvas.drawLine(prevTRX, prevTRY, currTRX, currTRY)
      
      # Bottom-right corner
      let currBRX = width - cornerRadius + xOffset
      let currBRY = height - cornerRadius + yOffset
      let prevBRX = width - cornerRadius + prevXOffset
      let prevBRY = height - cornerRadius + prevYOffset
      canvas.drawLine(prevBRX, prevBRY, currBRX, currBRY)
      
      # Bottom-left corner
      let currBLX = cornerRadius - xOffset
      let currBLY = height - cornerRadius + yOffset
      let prevBLX = cornerRadius - prevXOffset
      let prevBLY = height - cornerRadius + prevYOffset
      canvas.drawLine(prevBLX, prevBLY, currBLX, currBLY)
  
  # Add inner shadow effect for depth
  canvas.lineColor = rgb(40, 40, 40)  # Slightly lighter for inner shadow
  canvas.lineWidth = 1
  
  # Inner border
  let innerRadius = cornerRadius - 4
  if innerRadius > 0:
    # Inner straight edges
    canvas.drawLine(innerRadius, 6, width - innerRadius, 6)
    canvas.drawLine(width - 7, innerRadius, width - 7, height - innerRadius)
    canvas.drawLine(width - innerRadius, height - 7, innerRadius, height - 7)
    canvas.drawLine(6, height - innerRadius, 6, innerRadius)
    
    # Inner rounded corners (simplified)
    for i in 0..10:
      let angle = float(i) * PI / (2.0 * 10.0)
      let cosVal = cos(angle)
      let sinVal = sin(angle)
      let xOff = int(float(innerRadius) * cosVal)
      let yOff = int(float(innerRadius) * sinVal)
      
      if i > 0:
        let prevAngle = float(i-1) * PI / (2.0 * 10.0)
        let prevXOff = int(float(innerRadius) * cos(prevAngle))
        let prevYOff = int(float(innerRadius) * sin(prevAngle))
        
        # Inner corners
        canvas.drawLine(innerRadius - prevXOff + 6, innerRadius - prevYOff + 6, 
                       innerRadius - xOff + 6, innerRadius - yOff + 6)
        canvas.drawLine(width - innerRadius + prevXOff - 6, innerRadius - prevYOff + 6,
                       width - innerRadius + xOff - 6, innerRadius - yOff + 6)
        canvas.drawLine(width - innerRadius + prevXOff - 6, height - innerRadius + prevYOff - 6,
                       width - innerRadius + xOff - 6, height - innerRadius + yOff - 6)
        canvas.drawLine(innerRadius - prevXOff + 6, height - innerRadius + prevYOff - 6,
                       innerRadius - xOff + 6, height - innerRadius + yOff - 6)

# Add the drawing area to the container
container.add(drawingArea)

# Add the container to the window
window.add(container)

# Center the window on screen
window.centerOnScreen()

# Show the window
window.show()

# Start the GUI event loop
app.run()
