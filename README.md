# ZView - Borderless Window Application

A Nim application that creates a window with rounded corners and a dark theme.

## Features

- **Minimal Title Bar**: Empty title for a clean, modern look
- **Rounded Corners**: Custom-drawn smooth rounded corners with 25px radius
- **Dark Theme**:
  - Background: `#1b1b1b` (dark grey)
  - Border: `#333333` (grey)
  - Inner shadow effect for depth
- **Fixed Size**: 1200x1000 pixels
- **Centered**: Automatically centers on screen
- **Clean Interface**: No text, buttons, or UI elements

## Available Versions

1. **`zview.exe`** - Standard version with improved rounded corners
2. **`zview_borderless.exe`** - Enhanced version with better corner rendering
3. **`zview_final.exe`** - Best version with smooth 25px radius corners and depth effects

## Requirements

- Nim compiler (>= 1.6.0)
- nigui library (>= 0.2.6)

## Installation

1. Install Nim if you haven't already: https://nim-lang.org/install.html
2. Install dependencies:
   ```
   nimble install nigui
   ```

## Building

### Option 1: Using the build script (Windows)
```
build.bat
```

### Option 2: Manual compilation
```
nim c -d:release --app:gui zview.nim
```

## Running

After building, run the executable:
```
./zview.exe
```

## Customization

You can modify the following in `zview.nim`:
- Window dimensions: Change `window.width` and `window.height`
- Colors: Modify the `rgb()` values for background and border
- Corner radius: Adjust the `cornerRadius` variable in the draw event

## Notes

- The application uses custom drawing to achieve rounded corners
- The window is non-resizable by default
- Close the application using Alt+F4 or task manager since there's no title bar
