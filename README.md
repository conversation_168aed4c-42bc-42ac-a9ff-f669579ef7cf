# ZView - Borderless Window Application

A Nim application that creates a borderless window with rounded corners and a dark theme.

## Features

- **Borderless Window**: No title bar for a clean, modern look
- **Rounded Corners**: Custom-drawn rounded corners for aesthetic appeal
- **Dark Theme**: 
  - Background: `#1b1b1b` (dark grey)
  - Border: `#333333` (grey)
- **Fixed Size**: 1200x1000 pixels
- **Centered**: Automatically centers on screen

## Requirements

- Nim compiler (>= 1.6.0)
- nigui library (>= 0.2.6)

## Installation

1. Install Nim if you haven't already: https://nim-lang.org/install.html
2. Install dependencies:
   ```
   nimble install nigui
   ```

## Building

### Option 1: Using the build script (Windows)
```
build.bat
```

### Option 2: Manual compilation
```
nim c -d:release --app:gui zview.nim
```

## Running

After building, run the executable:
```
./zview.exe
```

## Customization

You can modify the following in `zview.nim`:
- Window dimensions: Change `window.width` and `window.height`
- Colors: Modify the `rgb()` values for background and border
- Corner radius: Adjust the `cornerRadius` variable in the draw event

## Notes

- The application uses custom drawing to achieve rounded corners
- The window is non-resizable by default
- Close the application using Alt+F4 or task manager since there's no title bar
